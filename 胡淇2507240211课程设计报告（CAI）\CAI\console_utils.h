#ifndef CONSOLE_UTILS_H
#define CONSOLE_UTILS_H

#include <conio.h> // 用于 _getch() 函数
#include <windows.h> // 用于 SetConsoleTextAttribute 和 GetStdHandle 函数
#include "global_defs.h"

// --- 函数原型声明 ---
void setColor(int color);                                  // 设置控制台颜色
void clearScreen();                                        // 清屏函数
void displayWelcome();                                     // 显示欢迎界面
Difficulty chooseDifficulty();                             // 用户选择难度级别
void displayQuestion(const char* question);                // 显示题目
void displayStatus(Difficulty difficulty, int score);      // 显示当前状态（难度和得分）
void displayHelp();                                        // 显示帮助信息

// --- 新增用户管理界面函数 ---
int showLoginMenu();                                       // 显示登录菜单
int userLogin(UserProfile* user);                          // 用户登录
int userRegister(UserProfile* user);                       // 用户注册
void displayUserInfo(const UserProfile* user);             // 显示用户信息
void displayLearningStats(const char* username);           // 显示学习统计
void displayRankingBoard();                                // 显示排行榜
int deleteCurrentUser(const char* username);              // 注销当前用户
int showMainMenuWithUser(const UserProfile* user);         // 显示带用户信息的主菜单
Difficulty selectPracticeDifficulty();                     // 选择练习难度

#endif // CONSOLE_UTILS_H