#include "file_manager.h"
#include <sys/stat.h>
#include <direct.h>

// 检查文件是否存在
int fileExists(const char* filename) {
    FILE* file;
    errno_t err = fopen_s(&file, filename, "r");
    if (err == 0 && file) {
        fclose(file);
        return 1;
    }
    return 0;
}

// 创建数据目录
int createDataDirectory() {
    struct stat st = {0};
    if (stat("data", &st) == -1) {
        return _mkdir("data");
    }
    return 0;
}

// 初始化数据文件
void initializeDataFiles() {
    createDataDirectory();

    // 如果用户数据文件不存在则创建空文件
    if (!fileExists(USER_DATA_FILE)) {
        FILE* file;
        errno_t err = fopen_s(&file, USER_DATA_FILE, "wb");
        if (err == 0 && file) {
            fclose(file);
        }
    }

    // 如果记录文件不存在则创建空文件
    if (!fileExists(RECORDS_FILE)) {
        FILE* file;
        errno_t err = fopen_s(&file, RECORDS_FILE, "wb");
        if (err == 0 && file) {
            fclose(file);
        }
    }
}

// 检查用户名是否存在
int userNameExists(const char* username) {
    FILE* file;
    errno_t err = fopen_s(&file, USER_DATA_FILE, "rb");
    if (err != 0 || !file) return 0;

    UserProfile temp_user;
    while (fread(&temp_user, sizeof(UserProfile), 1, file) == 1) {
        if (strcmp(temp_user.name, username) == 0) {
            fclose(file);
            return 1;
        }
    }
    fclose(file);
    return 0;
}

// 按用户名加载用户档案
int loadUserByName(const char* username, UserProfile* user) {
    FILE* file;
    errno_t err = fopen_s(&file, USER_DATA_FILE, "rb");
    if (err != 0 || !file) return 0;

    UserProfile temp_user;
    while (fread(&temp_user, sizeof(UserProfile), 1, file) == 1) {
        if (strcmp(temp_user.name, username) == 0) {
            *user = temp_user;
            user->last_login_time = time(NULL); // 更新最后登录时间
            fclose(file);
            saveUserProfile(user); // 保存更新的登录时间
            return 1;
        }
    }
    fclose(file);
    return 0;
}

// 创建新用户
int createNewUser(const char* name, const char* password, UserProfile* user) {
    if (userNameExists(name)) {
        return 0; // 用户名已存在
    }

    // 初始化用户数据
    strncpy_s(user->name, MAX_NAME_LEN, name, _TRUNCATE);
    strncpy_s(user->password, MAX_PASSWORD_LEN, password, _TRUNCATE);
    user->current_difficulty = ENTRY;
    user->total_score = 0;
    user->current_session_score = 0;
    user->registration_time = time(NULL);
    user->last_login_time = time(NULL);
    user->total_questions = 0;
    user->correct_answers = 0;
    user->total_study_time = 0.0;

    return saveUserProfile(user);
}

// 保存用户档案
int saveUserProfile(const UserProfile* user) {
    FILE* file;
    FILE* temp_file;
    fopen_s(&file, USER_DATA_FILE, "rb");
    errno_t err = fopen_s(&temp_file, "temp_users.dat", "wb");

    if (err != 0 || !temp_file) return 0;

    int user_updated = 0;
    UserProfile temp_user;

    // 如果原文件存在则复制其他用户数据
    if (file) {
        while (fread(&temp_user, sizeof(UserProfile), 1, file) == 1) {
            if (strcmp(temp_user.name, user->name) == 0) {
                // 写入更新的用户数据
                fwrite(user, sizeof(UserProfile), 1, temp_file);
                user_updated = 1;
            } else {
                // 写入其他用户数据
                fwrite(&temp_user, sizeof(UserProfile), 1, temp_file);
            }
        }
        fclose(file);
    }

    // 如果是新用户，追加到文件末尾
    if (!user_updated) {
        fwrite(user, sizeof(UserProfile), 1, temp_file);
    }

    fflush(temp_file); // 强制刷新缓冲区到磁盘
    fclose(temp_file);

    // 替换原文件
    remove(USER_DATA_FILE);
    rename("temp_users.dat", USER_DATA_FILE);

    return 1;
}



// 保存问题回答记录
int saveQuestionRecord(const QuestionRecord* record) {
    FILE* file;
    errno_t err = fopen_s(&file, RECORDS_FILE, "ab");
    if (err != 0 || !file) return 0;

    int result = fwrite(record, sizeof(QuestionRecord), 1, file);
    fflush(file); // 强制刷新缓冲区到磁盘
    fclose(file);

    return result == 1;
}

// 得到问题回答记录的数目
int getRecordCount(const char* username) {
    FILE* file;
    errno_t err = fopen_s(&file, RECORDS_FILE, "rb");
    if (err != 0 || !file) return 0;

    int count = 0;
    QuestionRecord temp_record;

    while (fread(&temp_record, sizeof(QuestionRecord), 1, file) == 1) {
        if (strcmp(temp_record.username, username) == 0) {
            count++;
        }
    }

    fclose(file);
    return count;
}

// 加载用户记录
int loadUserRecords(const char* username, QuestionRecord records[], int max_records) {
    FILE* file;
    errno_t err = fopen_s(&file, RECORDS_FILE, "rb");
    if (err != 0 || !file) return 0;

    int count = 0;
    QuestionRecord temp_record;

    while (fread(&temp_record, sizeof(QuestionRecord), 1, file) == 1 && count < max_records) {
        if (strcmp(temp_record.username, username) == 0) {
            records[count] = temp_record;
            count++;
        }
    }

    fclose(file);
    return count;
}

// 更新用户统计
void updateUserStats(UserProfile* user, const QuestionRecord* record) {
    user->total_questions++;
    if (record->is_correct) {
        user->correct_answers++;
    }
    user->total_study_time += record->time_spent;
}

// 计算学习统计
void calculateLearningStats(const char* username, LearningStats* stats) {
    // 初始化统计数据
    memset(stats, 0, sizeof(LearningStats));

    FILE* file;
    errno_t err = fopen_s(&file, RECORDS_FILE, "rb");
    if (err != 0 || !file) return;

    QuestionRecord temp_record;
    while (fread(&temp_record, sizeof(QuestionRecord), 1, file) == 1) {
        if (strcmp(temp_record.username, username) == 0) {
            stats->total_questions++;
            if (temp_record.is_correct) {
                stats->total_correct++;
            }

            // 按难度统计
            switch (temp_record.difficulty) {
            case ENTRY:
                stats->entry_questions++;
                if (temp_record.is_correct) stats->entry_correct++;
                break;
            case ADVANCED:
                stats->advanced_questions++;
                if (temp_record.is_correct) stats->advanced_correct++;
                break;
            case HELL:
                stats->hell_questions++;
                if (temp_record.is_correct) stats->hell_correct++;
                break;
            }

            stats->total_study_time += temp_record.time_spent;
        }
    }

    fclose(file);

    // 计算统计值
    if (stats->total_questions > 0) {
        stats->overall_accuracy = (double)stats->total_correct / stats->total_questions * 100.0;
        stats->avg_time_per_question = stats->total_study_time / stats->total_questions;
    }
}

// 保存配置
int saveConfig(const char* key, const char* value) {
    FILE* file;
    errno_t err = fopen_s(&file, CONFIG_FILE, "a");
    if (err != 0 || !file) return 0;

    fprintf(file, "%s=%s\n", key, value);
    fclose(file);
    return 1;
}

// 加载配置
int loadConfig(const char* key, char* value, int max_len) {
    FILE* file;
    errno_t err = fopen_s(&file, CONFIG_FILE, "r");
    if (err != 0 || !file) return 0;

    char line[256];
    char config_key[128];
    char config_value[128];

    while (fgets(line, sizeof(line), file)) {
        if (sscanf_s(line, "%127[^=]=%127s", config_key, (unsigned)sizeof(config_key), config_value, (unsigned)sizeof(config_value)) == 2) {
            if (strcmp(config_key, key) == 0) {
                strncpy_s(value, max_len, config_value, _TRUNCATE);
                fclose(file);
                return 1;
            }
        }
    }

    fclose(file);
    return 0;
}

// 生成学习报告
int generateLearningReport(const char* username) {
    UserProfile user;
    LearningStats stats;

    if (!loadUserByName(username, &user)) {
        return 0;
    }

    calculateLearningStats(username, &stats);

    FILE* file;
    errno_t err = fopen_s(&file, REPORT_FILE, "a");
    if (err != 0 || !file) return 0;

    // 添加用户分隔符
    fprintf(file, "\n");
    fprintf(file, "================================================================\n");
    fprintf(file, "                     用户 %s 的学习报告                        \n", username);
    fprintf(file, "================================================================\n");

    fprintf(file, "=== CAI 学习报告 ===\n\n");
    fprintf(file, "用户信息:\n");
    fprintf(file, "用户名: %s\n", user.name);
    fprintf(file, "当前难度: %s\n",
        user.current_difficulty == ENTRY ? "入门" :
        user.current_difficulty == ADVANCED ? "进阶" : "地狱");

    char reg_time[64], login_time[64];
    struct tm reg_tm, login_tm;
    localtime_s(&reg_tm, &user.registration_time);
    localtime_s(&login_tm, &user.last_login_time);
    strftime(reg_time, sizeof(reg_time), "%Y-%m-%d %H:%M:%S", &reg_tm);
    strftime(login_time, sizeof(login_time), "%Y-%m-%d %H:%M:%S", &login_tm);

    fprintf(file, "注册时间: %s\n", reg_time);
    fprintf(file, "最后登录: %s\n\n", login_time);

    fprintf(file, "学习统计:\n");
    fprintf(file, "总题目数: %d\n", stats.total_questions);
    fprintf(file, "总正确数: %d\n", stats.total_correct);
    fprintf(file, "总体正确率: %.1f%%\n", stats.overall_accuracy);
    fprintf(file, "平均每题用时: %.1f秒\n", stats.avg_time_per_question);
    fprintf(file, "总学习时间: %.1f分钟\n\n", stats.total_study_time / 60.0);

    fprintf(file, "各难度表现:\n");
    if (stats.entry_questions > 0) {
        fprintf(file, "入门级: %d/%d (%.1f%%)\n", stats.entry_correct, stats.entry_questions,
            (double)stats.entry_correct / stats.entry_questions * 100.0);
    }
    if (stats.advanced_questions > 0) {
        fprintf(file, "进阶级: %d/%d (%.1f%%)\n", stats.advanced_correct, stats.advanced_questions,
            (double)stats.advanced_correct / stats.advanced_questions * 100.0);
    }
    if (stats.hell_questions > 0) {
        fprintf(file, "地狱级: %d/%d (%.1f%%)\n", stats.hell_correct, stats.hell_questions,
            (double)stats.hell_correct / stats.hell_questions * 100.0);
    }

    time_t now = time(NULL);
    char report_time[64];
    struct tm now_tm;
    localtime_s(&now_tm, &now);
    strftime(report_time, sizeof(report_time), "%Y-%m-%d %H:%M:%S", &now_tm);
    fprintf(file, "\n报告生成时间: %s\n", report_time);

    // 添加结束分隔符
    fprintf(file, "================================================================\n");
    fprintf(file, "                        报告结束                              \n");
    fprintf(file, "================================================================\n\n");

    fclose(file);
    return 1;
}

// 获取所有用户信息
int getAllUsers(UserProfile users[], int max_users) {
    FILE* file;
    errno_t err = fopen_s(&file, USER_DATA_FILE, "rb");
    if (err != 0 || !file) return 0;

    int count = 0;
    UserProfile temp_user;

    while (fread(&temp_user, sizeof(UserProfile), 1, file) == 1 && count < max_users) {
        users[count] = temp_user;
        count++;
    }

    fclose(file);
    return count;
}

// 获取总分排行榜
int getTotalScoreRanking(RankingUser rankings[], int max_users) {
    UserProfile users[100]; // 假设最多100个用户
    int user_count = getAllUsers(users, 100);
    int ranking_count = 0;

    for (int i = 0; i < user_count && ranking_count < max_users; i++) {
        LearningStats stats;
        calculateLearningStats(users[i].name, &stats);

        RankingUser ranking;
        strncpy_s(ranking.username, MAX_NAME_LEN, users[i].name, _TRUNCATE);
        ranking.total_score = users[i].total_score;
        ranking.questions_count = stats.total_questions;
        ranking.correct_count = stats.total_correct;

        // 计算总体正确率和平均用时
        if (ranking.questions_count > 0) {
            ranking.accuracy = (double)ranking.correct_count / ranking.questions_count * 100.0;
            ranking.avg_time = stats.avg_time_per_question;
        } else {
            ranking.accuracy = 0.0;
            ranking.avg_time = 0.0;
        }

        // 只添加有答题记录的用户
        if (ranking.questions_count > 0) {
            rankings[ranking_count] = ranking;
            ranking_count++;
        }
    }

    return ranking_count;
}

// 按总分排序（降序）
void sortRankingByTotalScore(RankingUser rankings[], int count) {
    for (int i = 0; i < count - 1; i++) {
        for (int j = 0; j < count - 1 - i; j++) {
            if (rankings[j].total_score < rankings[j + 1].total_score) {
                RankingUser temp = rankings[j];
                rankings[j] = rankings[j + 1];
                rankings[j + 1] = temp;
            }
        }
    }
}

// 删除用户及其所有数据
int deleteUser(const char* username) {
    int success = 1;

    // 1. 删除用户档案
    FILE* file;
    FILE* temp_file;
    fopen_s(&file, USER_DATA_FILE, "rb");
    errno_t err = fopen_s(&temp_file, "temp_users.dat", "wb");

    if (err != 0 || !temp_file) return 0;

    int user_found = 0;
    UserProfile temp_user;

    if (file) {
        while (fread(&temp_user, sizeof(UserProfile), 1, file) == 1) {
            if (strcmp(temp_user.name, username) == 0) {
                user_found = 1; // 找到要删除的用户，不写入临时文件
            } else {
                fwrite(&temp_user, sizeof(UserProfile), 1, temp_file); // 保留其他用户
            }
        }
        fclose(file);
    }

    fflush(temp_file);
    fclose(temp_file);

    if (user_found) {
        remove(USER_DATA_FILE);
        rename("temp_users.dat", USER_DATA_FILE);
    } else {
        remove("temp_users.dat");
        success = 0; // 用户不存在
    }

    // 2. 删除用户的所有答题记录
    if (user_found) {
        FILE* records_file;
        FILE* temp_records_file;
        fopen_s(&records_file, RECORDS_FILE, "rb");
        errno_t err2 = fopen_s(&temp_records_file, "temp_records.dat", "wb");

        if (err2 == 0 && temp_records_file) {
            QuestionRecord temp_record;

            if (records_file) {
                while (fread(&temp_record, sizeof(QuestionRecord), 1, records_file) == 1) {
                    if (strcmp(temp_record.username, username) != 0) {
                        fwrite(&temp_record, sizeof(QuestionRecord), 1, temp_records_file); // 保留其他用户的记录
                    }
                }
                fclose(records_file);
            }

            fflush(temp_records_file);
            fclose(temp_records_file);

            remove(RECORDS_FILE);
            rename("temp_records.dat", RECORDS_FILE);
        } else {
            if (records_file) fclose(records_file);
        }
    }

    return success;
}
