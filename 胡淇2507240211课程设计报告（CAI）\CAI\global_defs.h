#ifndef GLOBAL_DEFS_H
#define GLOBAL_DEFS_H

#include <time.h>

// --- 常量定义 ---
#define MAX_SCORE 10      // 升级所需分数
#define MIN_SCORE 0       // 降级所需分数（当前主要用于判断是否需要降级）
#define MAX_ATTEMPTS 3    // 每道题最大尝试次数
#define MAX_EXPR_LEN 100  // 表达式字符串的最大长度
#define MAX_STACK_SIZE 50 // 操作数和操作符栈的最大大小

// --- 新增常量定义 ---
#define MAX_NAME_LEN 50   // 用户姓名最大长度
#define MAX_ID_LEN 20     // 用户ID最大长度
#define MAX_PASSWORD_LEN 30 // 用户密码最大长度
#define MAX_RECORDS 1000  // 最大题目记录数
#define MAX_SESSIONS 100  // 最大学习会话数
#define MAX_FILENAME 256  // 文件名最大长度

// --- 控制台颜色代码 ---
#define COLOR_DEFAULT 7   // 白色
#define COLOR_TITLE 11    // 亮青色
#define COLOR_CORRECT 10  // 亮绿色
#define COLOR_WRONG 12    // 亮红色
#define COLOR_PROMPT 14   // 黄色
#define COLOR_LEVEL_UP 13 // 亮品红色

// --- 枚举类型 ---
typedef enum { ENTRY, ADVANCED, HELL } Difficulty; // 难度级别：入门、进阶、地狱

// --- 结构体定义 ---

// 用户档案结构体
typedef struct {
    char name[MAX_NAME_LEN];        // 用户姓名
    char password[MAX_PASSWORD_LEN]; // 用户密码
    Difficulty current_difficulty;   // 当前难度级别
    int total_score;                // 总得分
    int current_session_score;      // 当前会话得分
    time_t registration_time;       // 注册时间
    time_t last_login_time;         // 最后登录时间
    int total_questions;            // 总题目数
    int correct_answers;            // 正确答案数
    double total_study_time;        // 总学习时间（秒）
} UserProfile;

// 题目记录结构体
typedef struct {
    char username[MAX_NAME_LEN];    // 用户名
    char question[MAX_EXPR_LEN];    // 题目内容
    int correct_answer;             // 正确答案
    int user_answer;                // 用户答案
    int attempts_used;              // 使用的尝试次数
    int is_correct;                 // 是否答对 (1-对, 0-错)
    Difficulty difficulty;          // 题目难度
    time_t timestamp;               // 答题时间戳
    double time_spent;              // 答题用时（秒）
} QuestionRecord;

// 学习会话结构体
typedef struct {
    time_t start_time;              // 开始时间
    time_t end_time;                // 结束时间
    int questions_count;            // 题目数量
    int correct_count;              // 正确数量
    Difficulty session_difficulty;  // 会话难度
    double session_duration;        // 会话时长（秒）
} StudySession;

// 学习统计结构体
typedef struct {
    int total_questions;            // 总题目数
    int total_correct;              // 总正确数
    double overall_accuracy;        // 总体正确率
    int entry_questions;            // 入门级题目数
    int entry_correct;              // 入门级正确数
    int advanced_questions;         // 进阶级题目数
    int advanced_correct;           // 进阶级正确数
    int hell_questions;             // 地狱级题目数
    int hell_correct;               // 地狱级正确数
    double avg_time_per_question;   // 平均每题用时
    int total_sessions;             // 总学习会话数
    double total_study_time;        // 总学习时间
} LearningStats;

// 排行榜用户信息结构体
typedef struct {
    char username[MAX_NAME_LEN];    // 用户名
    int questions_count;            // 该难度题目数
    int correct_count;              // 该难度正确数
    double accuracy;                // 该难度正确率
    double avg_time;                // 该难度平均用时
    int total_score;                // 总分
} RankingUser;

#endif // GLOBAL_DEFS_H