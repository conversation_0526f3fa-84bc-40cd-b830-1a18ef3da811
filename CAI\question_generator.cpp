#include "question_generator.h"
#include "expression_evaluator.h"
#include <string.h>

// 根据难度生成数学题目
void generateQuestion(Difficulty difficulty, char* question) {
    int num1, num2, num3;
    char ops_entry[] = "+-";      // 入门难度只使用加减法
    char ops_advanced[] = "+-*/"; // 进阶/地狱难度使用四则运算
    char op1, op2;
    int range_num; // 数字范围
    int calc_error; // 用于calculate函数

    switch (difficulty) {
    case ENTRY:
        range_num = 10;
        do {
            num1 = rand() % range_num + 1; // 1到10
            num2 = rand() % range_num + 1; // 1到10
            op1 = ops_entry[rand() % 2];   // 随机选择加或减
            sprintf_s(question, MAX_EXPR_LEN, "%d %c %d", num1, op1, num2);
            // 确保生成的题目有整数结果且无计算错误
        } while (calculate(question, &calc_error) < 0 || calc_error);
        break;

    case ADVANCED:
        range_num = 20;
        do {
            num1 = rand() % range_num + 1;
            num2 = rand() % range_num + 1;
            num3 = rand() % range_num + 1;
            op1 = ops_advanced[rand() % 4]; // 随机选择四则运算
            op2 = ops_advanced[rand() % 4];

            // 确保第一个运算符是除法时，被除数非零且结果为整数
            if (op1 == '/') {
                while (num2 == 0 || num1 % num2 != 0) {
                    num1 = rand() % range_num + 1;
                    num2 = rand() % range_num + 1;
                }
            }
            // 确保第二个运算符是除法时，除数非零
            if (op2 == '/') {
                while (num3 == 0) {
                    num3 = rand() % range_num + 1;
                }
            }
            sprintf_s(question, MAX_EXPR_LEN, "(%d %c %d) %c %d", num1, op1, num2, op2, num3);
             // 确保生成的题目有整数结果且无计算错误
        } while (calculate(question, &calc_error) < 0 || calc_error);
        break;

    case HELL:
        range_num = 50;
        do {
            num1 = rand() % range_num + 10; // 数字范围更大，从10到59
            num2 = rand() % range_num + 10;
            num3 = rand() % range_num + 10;
            op1 = ops_advanced[rand() % 4];
            op2 = ops_advanced[rand() % 4];

            // 确保第一个运算符是除法时，被除数非零且结果为整数
            if (op1 == '/') {
                while (num2 == 0 || num1 % num2 != 0) {
                    num1 = rand() % range_num + 10;
                    num2 = rand() % range_num + 10;
                }
            }
            // 确保第二个运算符是除法时，除数非零
            if (op2 == '/') {
                while (num3 == 0) {
                    num3 = rand() % range_num + 10;
                }
            }
            sprintf_s(question, MAX_EXPR_LEN, "(%d %c %d) %c %d", num1, op1, num2, op2, num3);
            // 确保生成的题目有整数结果且无计算错误
        } while (calculate(question, &calc_error) < 0 || calc_error);
        break;
    }
} 