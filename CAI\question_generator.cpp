#include "question_generator.h"
#include "expression_evaluator.h"
#include <string.h>

// 根据难度生成数学题目
void generateQuestion(Difficulty difficulty, char* question) {
    int num1, num2, num3;
    char ops_entry[] = "+-";      // 入门难度只使用加减法
    char ops_advanced[] = "+-*/"; // 进阶/地狱难度使用四则运算
    char op1, op2;
    int range_num; // 数字范围
    int calc_error; // 用于calculate函数

    switch (difficulty) {
    case ENTRY:
        range_num = 10;
        do {
            num1 = rand() % range_num + 1; // 1到10
            num2 = rand() % range_num + 1; // 1到10
            op1 = ops_entry[rand() % 2];   // 随机选择加或减
            sprintf_s(question, MAX_EXPR_LEN, "%d %c %d", num1, op1, num2);
            // 确保生成的题目有整数结果且无计算错误
        } while (calculate(question, &calc_error) < 0 || calc_error);
        break;

    case ADVANCED:
        range_num = 20;
        do {
            num1 = rand() % range_num + 1;
            num2 = rand() % range_num + 1;
            num3 = rand() % range_num + 1;
            op1 = ops_advanced[rand() % 4]; // 随机选择四则运算
            op2 = ops_advanced[rand() % 4];

            // 确保第一个运算符是除法时，被除数非零且结果为整数
            if (op1 == '/') {
                while (num2 == 0 || num1 % num2 != 0) {
                    num1 = rand() % range_num + 1;
                    num2 = rand() % range_num + 1;
                }
            }
            // 确保第二个运算符是除法时，除数非零
            if (op2 == '/') {
                while (num3 == 0) {
                    num3 = rand() % range_num + 1;
                }
            }
            sprintf_s(question, MAX_EXPR_LEN, "(%d %c %d) %c %d", num1, op1, num2, op2, num3);
             // 确保生成的题目有整数结果且无计算错误
        } while (calculate(question, &calc_error) < 0 || calc_error);
        break;

    case HELL:
        range_num = 50;
        do {
            // 随机选择地狱模式的表达式类型
            int hell_type = rand() % 4;

            switch (hell_type) {
            case 0: // 类型1: ((a op b) op c) op d - 嵌套括号
                {
                    int num4 = rand() % range_num + 5;
                    num1 = rand() % range_num + 5;
                    num2 = rand() % range_num + 5;
                    num3 = rand() % range_num + 5;
                    op1 = ops_advanced[rand() % 4];
                    op2 = ops_advanced[rand() % 4];
                    char op3 = ops_advanced[rand() % 4];

                    // 处理除法确保整数结果
                    if (op1 == '/') {
                        while (num2 == 0 || num1 % num2 != 0) {
                            num1 = rand() % range_num + 5;
                            num2 = rand() % range_num + 5;
                        }
                    }
                    if (op2 == '/') {
                        while (num3 == 0) {
                            num3 = rand() % range_num + 5;
                        }
                    }
                    if (op3 == '/') {
                        while (num4 == 0) {
                            num4 = rand() % range_num + 5;
                        }
                    }
                    sprintf_s(question, MAX_EXPR_LEN, "((%d %c %d) %c %d) %c %d",
                             num1, op1, num2, op2, num3, op3, num4);
                }
                break;

            case 1: // 类型2: (a op b) op (c op d) - 双括号并列
                {
                    int num4 = rand() % range_num + 5;
                    num1 = rand() % range_num + 5;
                    num2 = rand() % range_num + 5;
                    num3 = rand() % range_num + 5;
                    op1 = ops_advanced[rand() % 4];
                    op2 = ops_advanced[rand() % 4];
                    char op3 = ops_advanced[rand() % 4];

                    // 处理除法
                    if (op1 == '/') {
                        while (num2 == 0 || num1 % num2 != 0) {
                            num1 = rand() % range_num + 5;
                            num2 = rand() % range_num + 5;
                        }
                    }
                    if (op3 == '/') {
                        while (num4 == 0 || num3 % num4 != 0) {
                            num3 = rand() % range_num + 5;
                            num4 = rand() % range_num + 5;
                        }
                    }
                    sprintf_s(question, MAX_EXPR_LEN, "(%d %c %d) %c (%d %c %d)",
                             num1, op1, num2, op2, num3, op3, num4);
                }
                break;

            case 2: // 类型3: a op (b op c) op d - 中间括号
                {
                    int num4 = rand() % range_num + 5;
                    num1 = rand() % range_num + 5;
                    num2 = rand() % range_num + 5;
                    num3 = rand() % range_num + 5;
                    op1 = ops_advanced[rand() % 4];
                    op2 = ops_advanced[rand() % 4];
                    char op3 = ops_advanced[rand() % 4];

                    // 处理除法
                    if (op2 == '/') {
                        while (num3 == 0 || num2 % num3 != 0) {
                            num2 = rand() % range_num + 5;
                            num3 = rand() % range_num + 5;
                        }
                    }
                    if (op3 == '/') {
                        while (num4 == 0) {
                            num4 = rand() % range_num + 5;
                        }
                    }
                    sprintf_s(question, MAX_EXPR_LEN, "%d %c (%d %c %d) %c %d",
                             num1, op1, num2, op2, num3, op3, num4);
                }
                break;

            case 3: // 类型4: 五个数字的复杂表达式 (a op b op c) op (d op e)
                {
                    int num4 = rand() % range_num + 5;
                    int num5 = rand() % range_num + 5;
                    num1 = rand() % range_num + 5;
                    num2 = rand() % range_num + 5;
                    num3 = rand() % range_num + 5;
                    op1 = ops_advanced[rand() % 4];
                    op2 = ops_advanced[rand() % 4];
                    char op3 = ops_advanced[rand() % 4];
                    char op4 = ops_advanced[rand() % 4];

                    // 处理除法
                    if (op1 == '/') {
                        while (num2 == 0 || num1 % num2 != 0) {
                            num1 = rand() % range_num + 5;
                            num2 = rand() % range_num + 5;
                        }
                    }
                    if (op2 == '/') {
                        while (num3 == 0) {
                            num3 = rand() % range_num + 5;
                        }
                    }
                    if (op4 == '/') {
                        while (num5 == 0 || num4 % num5 != 0) {
                            num4 = rand() % range_num + 5;
                            num5 = rand() % range_num + 5;
                        }
                    }
                    sprintf_s(question, MAX_EXPR_LEN, "(%d %c %d %c %d) %c (%d %c %d)",
                             num1, op1, num2, op2, num3, op3, num4, op4, num5);
                }
                break;
            }

            // 确保生成的题目有整数结果且无计算错误
        } while (calculate(question, &calc_error) < 0 || calc_error);
        break;
    }
}