#ifndef EXPRESSION_EVALUATOR_H
#define EXPRESSION_EVALUATOR_H

#include <string.h>
#include "global_defs.h"

// --- 表达式求值辅助函数 ---
int getOperatorPrecedence(char op);                         // 获取操作符优先级
int applyOperation(int a, int b, char op, int* error);      // 执行运算
int isOperator(char c);                                     // 判断字符是否为操作符

// --- 改进后的表达式求值函数（类似调度场算法原理） ---
int calculate(const char* s, int* error);                  // 计算表达式值（改进后的函数）

#endif // EXPRESSION_EVALUATOR_H 