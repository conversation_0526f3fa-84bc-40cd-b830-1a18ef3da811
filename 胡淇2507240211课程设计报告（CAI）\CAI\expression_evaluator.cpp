#include "expression_evaluator.h"
#include <stdlib.h>
#include <string.h>

// 获取操作符的优先级
// 加减法优先级为1，乘除法优先级为2，其他为0
int getOperatorPrecedence(char op) {
    if (op == '+' || op == '-') return 1;
    if (op == '*' || op == '/') return 2;
    return 0; // 用于括号或无效操作符
}

// 执行两个操作数的运算
int applyOperation(int a, int b, char op, int* error) {
    switch (op) {
    case '+': return a + b;
    case '-': return a - b;
    case '*': return a * b;
    case '/':
        if (b == 0) { // 除数为零错误
            *error = 1;
            return 0;
        }
        if (a % b != 0) { // 如果要求整数除法，确保没有余数
            *error = 1; // 结果不是整数
            return 0;
        }
        return a / b;
    }
    *error = 1; // 无效操作符
    return 0;
}

// 检查字符是否为有效的运算符
int isOperator(char c) {
    return (c == '+' || c == '-' || c == '*' || c == '/');
}

// --- 改进后的表达式求值函数（类似调度场算法原理） ---
// 该函数计算算术表达式字符串的值。
// 它处理加、减、乘、除和括号。
// 它使用两个栈：一个用于数字，一个用于操作符。
// 返回计算结果，如果发生错误则返回0并设置错误标志。
int calculate(const char* s, int* error) {
    int operands[MAX_STACK_SIZE];    // 操作数栈
    char operators[MAX_STACK_SIZE];  // 操作符栈
    int op_top = -1;                 // 操作数栈顶索引
    int opr_top = -1;                // 操作符栈顶索引
    int i = 0;
    int len = strlen(s);

    *error = 0; // 初始化错误标志

    while (i < len) {
        if (s[i] == ' ') { // 跳过空格
            i++;
            continue;
        }

        if (s[i] >= '0' && s[i] <= '9') { // 如果是数字
            int num = 0;
            while (i < len && s[i] >= '0' && s[i] <= '9') {
                num = num * 10 + (s[i] - '0');
                i++;
            }
            i--; // 补偿外层循环的 i++
            if (op_top >= MAX_STACK_SIZE - 1) { *error = 1; return 0; } // 栈溢出检查
            operands[++op_top] = num; // 将数字压入操作数栈
        }
        else if (s[i] == '(') { // 如果是左括号
            if (opr_top >= MAX_STACK_SIZE - 1) { *error = 1; return 0; } // 栈溢出检查
            operators[++opr_top] = '('; // 将左括号压入操作符栈
        }
        else if (s[i] == ')') { // 如果是右括号
            // 弹出操作符，直到遇到左括号
            while (opr_top != -1 && operators[opr_top] != '(') {
                if (op_top < 1) { *error = 1; return 0; } // 操作数不足
                int val2 = operands[op_top--]; // 弹出第二个操作数
                int val1 = operands[op_top--]; // 弹出第一个操作数
                char op = operators[opr_top--]; // 弹出操作符
                int res = applyOperation(val1, val2, op, error); // 执行运算
                if (*error) return 0; // 如果运算出错，立即返回
                operands[++op_top] = res; // 将结果压入操作数栈
            }
            if (opr_top == -1 || operators[opr_top] != '(') { *error = 1; return 0; } // 括号不匹配错误
            opr_top--; // 弹出 '('
        }
        else if (isOperator(s[i])) { // 如果是操作符
            // 弹出栈顶优先级高于或等于当前操作符的操作符并执行运算
            while (opr_top != -1 && getOperatorPrecedence(operators[opr_top]) >= getOperatorPrecedence(s[i])) {
                if (op_top < 1) { *error = 1; return 0; } // 操作数不足
                int val2 = operands[op_top--];
                int val1 = operands[op_top--];
                char op = operators[opr_top--];
                int res = applyOperation(val1, val2, op, error);
                if (*error) return 0;
                operands[++op_top] = res;
            }
            if (opr_top >= MAX_STACK_SIZE - 1) { *error = 1; return 0; } // 栈溢出检查
            operators[++opr_top] = s[i]; // 将当前操作符压入栈
        }
        else {
            *error = 1; // 表达式中包含无效字符
            return 0;
        }
        i++; // 移动到下一个字符
    }

    // 处理栈中剩余的操作符
    while (opr_top != -1) {
        if (op_top < 1) { *error = 1; return 0; } // 操作数不足
        int val2 = operands[op_top--];
        int val1 = operands[op_top--];
        char op = operators[opr_top--];
        int res = applyOperation(val1, val2, op, error);
        if (*error) return 0;
        operands[++op_top] = res;
    }

    if (op_top != 0) { *error = 1; return 0; } // 表达式格式错误（例如多余的操作数）
    return operands[0]; // 返回最终结果
} 