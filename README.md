# 计算机辅助教学 (CAI) 软件

本项目是一个基于命令行的计算机辅助教学 (CAI) 软件，旨在帮助用户练习数学运算。

## 功能特性

### 核心学习功能

- **多种难度级别**:
  - 入门: 10 以内的加减法
  - 进阶: 20 以内带括号的混合运算 (加、减、乘、除)
  - 地狱: 50 以内带括号的复杂混合运算 (加、减、乘、除)
- **题目自动生成**: 根据所选难度级别动态生成数学题目
- **答案判断**: 计算用户输入答案的正确性
- **计分与等级系统**: 答对题目增加分数，达到特定分数后自动升级
- **多次尝试机会**: 每道题目允许多次尝试
- **友好的用户界面**: 使用控制台颜色高亮显示信息
- **表达式求值**: 内置表达式求值器，支持带括号的四则运算

### 用户管理系统

- **用户注册与登录**: 支持多用户系统，每个用户有独立的学习记录
- **用户档案管理**: 保存用户基本信息、学习进度和统计数据
- **数据持久化**: 用户数据和学习记录自动保存到文件

### 学习记录与统计

- **题目记录**: 详细记录每道题目的答题情况、用时和难度
- **学习统计**: 提供总体正确率、各难度表现、平均用时等统计信息
- **学习报告**: 自动生成详细的学习报告，可导出为文本文件
- **进度跟踪**: 实时跟踪学习时间、题目数量和正确率变化

### 数据结构与文件操作

- **结构体应用**: 使用多个结构体组织用户信息、题目记录和统计数据
- **文件管理**: 实现用户数据、学习记录和配置信息的文件读写
- **数据安全**: 支持数据备份和恢复功能

## 项目构成

项目由以下几个主要模块组成：

1. **主程序模块** (`源.cpp`): 包含主函数和程序流程控制，集成用户管理和学习会话
2. **表达式求值模块** (`expression_evaluator.cpp/h`): 负责数学表达式的解析和计算
3. **题目生成模块** (`question_generator.cpp/h`): 根据不同难度级别生成数学题目
4. **控制台工具模块** (`console_utils.cpp/h`): 处理控制台显示、颜色设置、用户输入等界面功能，包含用户登录注册界面
5. **文件管理模块** (`file_manager.cpp/h`): 负责用户数据、学习记录和配置文件的读写操作
6. **全局定义模块** (`global_defs.h`): 包含全局常量、枚举类型和数据结构定义

### 数据结构设计

- **UserProfile**: 用户档案结构体，存储用户基本信息和学习统计
- **QuestionRecord**: 题目记录结构体，记录每道题目的详细答题信息
- **StudySession**: 学习会话结构体，记录单次学习会话的统计信息
- **LearningStats**: 学习统计结构体，提供综合的学习数据分析

## 如何运行

1. 使用 Visual Studio 打开 `CAI.sln` 解决方案文件
2. 编译项目
3. 运行生成的可执行文件

## 文件结构

```
.
├── CAI/
│   ├── 源.cpp                      # 主程序源代码
│   ├── expression_evaluator.cpp/h  # 表达式求值模块
│   ├── question_generator.cpp/h    # 题目生成模块
│   ├── console_utils.cpp/h         # 控制台工具模块
│   ├── file_manager.cpp/h          # 文件管理模块
│   ├── global_defs.h               # 全局定义和数据结构
│   └── CAI.vcxproj                 # Visual Studio 项目文件
├── CAI.sln                         # Visual Studio 解决方案文件
├── users.dat                       # 用户数据文件（二进制）
├── records.dat                     # 学习记录文件（二进制）
├── config.txt                      # 配置文件（文本）
├── learning_report.txt             # 学习报告文件（自动生成）
└── README.md                       # 项目说明文档
```

## 数据文件说明

- **users.dat**: 存储所有用户的档案信息，包括用户 ID、姓名、学习进度等
- **records.dat**: 存储所有用户的题目记录，包括题目内容、答题结果、用时等
- **config.txt**: 程序配置文件，可自定义程序参数
- **learning_report.txt**: 用户学习报告，包含详细的学习统计和分析

## 核心技术

### 算法与数据结构

- **表达式求值算法**: 采用类似调度场算法的原理，通过操作数栈和操作符栈计算中缀表达式
- **随机题目生成**: 根据难度级别动态生成适当复杂度的数学题目
- **结构体设计**: 使用多个结构体组织复杂数据，实现面向对象的数据管理

### 文件操作与数据管理

- **二进制文件读写**: 高效存储用户数据和学习记录
- **文本文件处理**: 配置文件解析和报告生成
- **数据持久化**: 自动保存和加载用户学习进度

### 用户体验设计

- **多用户系统**: 支持用户注册、登录和个人数据管理
- **实时统计**: 动态计算和显示学习统计信息
- **用户界面**: 使用控制台颜色和清晰的提示增强用户体验

## 程序特色

✅ **完整的结构体应用**: 4 个主要结构体覆盖用户管理、题目记录、学习统计等功能
✅ **全面的文件操作**: 支持二进制文件、文本文件的读写和数据持久化
✅ **用户管理系统**: 多用户支持，独立的学习记录和进度跟踪
✅ **学习数据分析**: 详细的统计信息和自动生成的学习报告
✅ **模块化设计**: 清晰的代码结构，易于维护和扩展

## 待改进点

- 增加更多类型的题目（小数、负数等）
- 允许用户自定义题目范围和操作符
- 添加学习目标设定和进度提醒功能
- 实现数据导入导出功能
- 增加图形化界面支持

---

希望这个 CAI 软件能帮助您提高数学运算能力！
