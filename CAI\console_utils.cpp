#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "console_utils.h"
#include "file_manager.h"

extern int createNewUser(const char* name, const char* password, UserProfile* user);
extern int loadUserByName(const char* username, UserProfile* user);
extern void calculateLearningStats(const char* username, LearningStats* stats);
extern int getTotalScoreRanking(RankingUser rankings[], int max_users);
extern void sortRankingByTotalScore(RankingUser rankings[], int count);
extern int deleteUser(const char* username);

void setColor(int color) {
    SetConsoleTextAttribute(GetStdHandle(STD_OUTPUT_HANDLE), color);
}

void clearScreen() {
    system("cls");
}

void displayWelcome() {
    setColor(COLOR_TITLE);
    printf("\n");
    printf("╔══════════════════════════════════════════════════════════════╗\n");
    printf("║                                                              ║\n");
    printf("║    🎓 欢迎使用计算机辅助教学(CAI)软件 🎓                    ║\n");
    printf("║                                                              ║\n");
    printf("║         🌟 让数学学习变得更有趣、更高效！ 🌟                ║\n");
    printf("║                                                              ║\n");
    printf("║    📚 智能练习  🏆 排行榜  📊 学习统计  📄 学习报告         ║\n");
    printf("║                                                              ║\n");
    setColor(COLOR_PROMPT);
    printf("║              ✨ 开始你的数学学习之旅吧！ ✨                  ║\n");
    setColor(COLOR_TITLE);
    printf("║                                                              ║\n");
    printf("╚══════════════════════════════════════════════════════════════╝\n");
    printf("\n");
    setColor(COLOR_DEFAULT);
}

Difficulty chooseDifficulty() {
    int choice_char;
    while (1) {
        clearScreen();
        displayWelcome();

        printf("----------------------------------------------------------------\n");
        printf("                        选择难度级别                          \n");
        printf("----------------------------------------------------------------\n");
        printf("                                                                \n");
        printf("  [1] 入门级 - 10以内加减法                                   \n");
        printf("  [2] 进阶级 - 20以内混合运算，含括号                         \n");
        printf("  [3] 地狱级 - 50以内复杂混合运算，含括号                     \n");
        printf("                                                                \n");
        printf("----------------------------------------------------------------\n");

        setColor(COLOR_PROMPT);
        printf("\n请选择(1 - 3): ");
        setColor(COLOR_DEFAULT);

        choice_char = _getch();
        printf("%c\n", choice_char);

        if (choice_char >= '1' && choice_char <= '3') {
            return (Difficulty)(choice_char - '1');
        }
        else {
            setColor(COLOR_WRONG);
            printf("无效输入，请重新选择!\n");
            setColor(COLOR_LEVEL_UP);
            Sleep(100);
        }
    }
}

void displayQuestion(const char* question) {
    printf("\n");
    printf("----------------------------------------------------------------\n");
    printf("                           题目                               \n");
    printf("----------------------------------------------------------------\n");

    setColor(COLOR_PROMPT);
    printf("  请计算: ");
    setColor(COLOR_DEFAULT);
    printf("%s\n", question);

    printf("----------------------------------------------------------------\n");

    setColor(COLOR_PROMPT);
    printf("\n答案: ");
    setColor(COLOR_DEFAULT);
}

void displayStatus(Difficulty difficulty, int score) {
    printf("\n");
    printf("================================================================\n");
    printf("                          学习状态                            \n");
    printf("================================================================\n");

    setColor(COLOR_PROMPT);
    printf("  当前级别: ");
    setColor(COLOR_DEFAULT);
    switch (difficulty) {
    case ENTRY: printf("入门级"); break;
    case ADVANCED: printf("进阶级"); break;
    case HELL: printf("地狱级"); break;
    }

    setColor(COLOR_PROMPT);
    printf("                当前得分: ");
    setColor(COLOR_DEFAULT);
    printf("%d/%d\n", score, MAX_SCORE);

    printf("================================================================\n");
    setColor(COLOR_DEFAULT);
}

void displayHelp() {
    setColor(COLOR_PROMPT);
    printf("📖 详细操作指南 📖\n\n");
    setColor(COLOR_DEFAULT);

    printf("1. 🎯 开始练习：进入算术练习模式\n");
    printf("   • 系统会根据当前难度生成题目\n");
    printf("   • 每次练习10道题\n");
    printf("   • 实时计分：入门级1分/题，进阶级3分/题，地狱级5分/题\n");
    printf("   • 连续答对可以升级难度\n");
    printf("   • 输入答案后按回车键提交\n");
    printf("   • 输入'q'可以返回主菜单\n\n");

    printf("2. ❓ 查看帮助：显示本操作指南\n\n");

    printf("3. 👤 查看用户信息：显示个人学习数据\n");
    printf("   • 用户名、当前难度级别\n");
    printf("   • 总分、总题数、正确率\n");
    printf("   • 注册时间、最后登录时间\n\n");

    printf("4. 📊 查看学习统计：显示详细学习数据\n");
    printf("   • 各难度级别的表现\n");
    printf("   • 总体学习时间和正确率\n\n");

    printf("5. 📄 生成学习报告：创建学习报告文件\n");
    printf("   • 生成详细的学习报告\n");
    printf("   • 保存到learning_report.txt文件\n\n");

    printf("6. ⚙️  选择练习难度：手动选择练习难度\n");
    printf("   • 入门级：10以内加减法 (1分/题)\n");
    printf("   • 进阶级：20以内混合运算 (3分/题)\n");
    printf("   • 地狱级：50以内复杂运算 (5分/题)\n\n");

    printf("7. 🏆 查看排行榜：查看总分排行榜\n");
    printf("   • 按总分排序显示所有用户排名\n");
    printf("   • 激励学习，增加竞争性\n\n");

    printf("8. ⚠️  注销用户：删除当前用户及所有数据\n");
    printf("   • 需要输入'DELETE'确认\n");
    printf("   • 将删除所有用户数据，无法撤销\n\n");

    printf("9. 🚪 退出登录：返回登录界面\n\n");

    printf("10. ❌ 退出程序：关闭软件\n\n");

    setColor(COLOR_PROMPT);
    printf("💡 小贴士：\n");
    setColor(COLOR_DEFAULT);
    printf("• 答对题目可获得分数，累计%d分可升级\n", MAX_SCORE);
    printf("• 每题最多有%d次尝试机会\n", MAX_ATTEMPTS);
    printf("• 总分越高，排行榜排名越靠前\n");
    printf("• 定期查看学习统计，了解自己的进步\n");
}


int showLoginMenu() {
    clearScreen();
    displayWelcome();

    setColor(COLOR_PROMPT);
    printf("用户系统\n\n");
    setColor(COLOR_DEFAULT);
    printf("1. 用户登录\n");
    printf("2. 新用户注册\n");
    printf("3. 退出程序\n");

    int choice;
    while (1) {
        setColor(COLOR_PROMPT);
        printf("请选择 (1 - 3): ");
        setColor(COLOR_DEFAULT);
        if (scanf_s("%d", &choice) == 1 && choice >= 1 && choice <= 3) {
            while (getchar() != '\n');
            return choice;
        } else {
            setColor(COLOR_WRONG);
            printf("输入无效，请重试！\n");
            setColor(COLOR_DEFAULT);
            while (getchar() != '\n');
            Sleep(1000);
        }
    }
}

int userLogin(UserProfile* user) {
    char username[MAX_NAME_LEN];
    char password[MAX_PASSWORD_LEN];

    clearScreen();
    displayWelcome();

    setColor(COLOR_PROMPT);
    printf("用户登录\n\n");
    setColor(COLOR_DEFAULT);
    printf("请输入用户名: ");

    if (fgets(username, sizeof(username), stdin) == NULL) {
        return 0;
    }
    username[strcspn(username, "\n")] = '\0';

    printf("请输入密码: ");
    if (fgets(password, sizeof(password), stdin) == NULL) {
        return 0;
    }
    password[strcspn(password, "\n")] = '\0';

    if (loadUserByName(username, user)) {
        if (strcmp(user->password, password) == 0) {
            setColor(COLOR_CORRECT);
            printf("\n登录成功！欢迎回来，%s！\n", user->name);
            setColor(COLOR_DEFAULT);
            Sleep(1500);
            return 1;
        } else {
            setColor(COLOR_WRONG);
            printf("\n密码错误！\n");
            setColor(COLOR_DEFAULT);
            Sleep(2000);
            return 0;
        }
    } else {
        setColor(COLOR_WRONG);
        printf("\n用户名不存在，请检查用户名或先注册！\n");
        setColor(COLOR_DEFAULT);
        Sleep(2000);
        return 0;
    }
}

int userRegister(UserProfile* user) {
    char name[MAX_NAME_LEN];
    char password[MAX_PASSWORD_LEN];
    char confirm_password[MAX_PASSWORD_LEN];

    clearScreen();
    displayWelcome();

    setColor(COLOR_PROMPT);
    printf("新用户注册\n\n");
    setColor(COLOR_DEFAULT);

    printf("请输入您的用户名: ");
    if (fgets(name, sizeof(name), stdin) == NULL) {
        return 0;
    }
    name[strcspn(name, "\n")] = '\0';

    printf("请输入密码: ");
    if (fgets(password, sizeof(password), stdin) == NULL) {
        return 0;
    }
    password[strcspn(password, "\n")] = '\0';

    printf("请确认密码: ");
    if (fgets(confirm_password, sizeof(confirm_password), stdin) == NULL) {
        return 0;
    }
    confirm_password[strcspn(confirm_password, "\n")] = '\0';

    if (strlen(name) == 0 || strlen(password) == 0) {
        setColor(COLOR_WRONG);
        printf("\n用户名和密码不能为空！\n");
        setColor(COLOR_DEFAULT);
        Sleep(2000);
        return 0;
    }

    if (strcmp(password, confirm_password) != 0) {
        setColor(COLOR_WRONG);
        printf("\n两次输入的密码不一致！\n");
        setColor(COLOR_DEFAULT);
        Sleep(2000);
        return 0;
    }

    if (createNewUser(name, password, user)) {
        setColor(COLOR_CORRECT);
        printf("\n注册成功！欢迎，%s！\n", user->name);
        setColor(COLOR_DEFAULT);
        Sleep(1500);
        return 1;
    } else {
        setColor(COLOR_WRONG);
        printf("\n注册失败！用户名可能已存在！\n");
        setColor(COLOR_DEFAULT);
        Sleep(2000);
        return 0;
    }
}

void displayUserInfo(const UserProfile* user) {
    setColor(COLOR_PROMPT);
    printf("\n用户信息:\n");
    setColor(COLOR_DEFAULT);
    printf("用户名: %s\n", user->name);
    printf("当前难度: ");
    switch (user->current_difficulty) {
    case ENTRY: printf("入门级"); break;
    case ADVANCED: printf("进阶级"); break;
    case HELL: printf("地狱级"); break;
    }
    printf("\n总分: %d\n", user->total_score);
    printf("总题数: %d\n", user->total_questions);
    printf("正确答案数: %d\n", user->correct_answers);
    if (user->total_questions > 0) {
        printf("正确率: %.1f%%\n", (double)user->correct_answers / user->total_questions * 100.0);
    }
    printf("总学习时间: %.1f 分钟\n", user->total_study_time / 60.0);

    char reg_time[64], login_time[64];
    struct tm reg_tm, login_tm;
    localtime_s(&reg_tm, &user->registration_time);
    localtime_s(&login_tm, &user->last_login_time);
    strftime(reg_time, sizeof(reg_time), "%Y-%m-%d %H:%M:%S", &reg_tm);
    strftime(login_time, sizeof(login_time), "%Y-%m-%d %H:%M:%S", &login_tm);
    printf("注册时间: %s\n", reg_time);
    printf("最后登录: %s\n", login_time);
}

void displayLearningStats(const char* username) {
    LearningStats stats;
    calculateLearningStats(username, &stats);

    setColor(COLOR_PROMPT);
    printf("\n详细学习统计:\n");
    setColor(COLOR_DEFAULT);
    printf("总题目数: %d\n", stats.total_questions);
    printf("总正确数: %d\n", stats.total_correct);
    printf("总体正确率: %.1f%%\n", stats.overall_accuracy);
    printf("平均每题用时: %.1f 秒\n", stats.avg_time_per_question);
    printf("总学习时间: %.1f 分钟\n\n", stats.total_study_time / 60.0);

    setColor(COLOR_PROMPT);
    printf("各难度表现:\n");
    setColor(COLOR_DEFAULT);
    if (stats.entry_questions > 0) {
        printf("入门级: %d/%d (%.1f%%)\n", stats.entry_correct, stats.entry_questions,
            (double)stats.entry_correct / stats.entry_questions * 100.0);
    }
    if (stats.advanced_questions > 0) {
        printf("进阶级: %d/%d (%.1f%%)\n", stats.advanced_correct, stats.advanced_questions,
            (double)stats.advanced_correct / stats.advanced_questions * 100.0);
    }
    if (stats.hell_questions > 0) {
        printf("地狱级: %d/%d (%.1f%%)\n", stats.hell_correct, stats.hell_questions,
            (double)stats.hell_correct / stats.hell_questions * 100.0);
    }
}

int showMainMenuWithUser(const UserProfile* user) {
    clearScreen();
    displayWelcome();

    setColor(COLOR_PROMPT);
    printf("当前用户: %s\n", user->name);
    printf("当前难度: ");
    switch (user->current_difficulty) {
    case ENTRY: printf("入门级"); break;
    case ADVANCED: printf("进阶级"); break;
    case HELL: printf("地狱级"); break;
    }
    printf(" | 当前得分: %d/%d\n", user->current_session_score, MAX_SCORE);
    setColor(COLOR_DEFAULT);
    printf("--------------------------------\n\n");

    setColor(COLOR_PROMPT);
    printf("主菜单\n\n");
    setColor(COLOR_DEFAULT);
    printf("1. 开始练习\n");
    printf("2. 查看帮助\n");
    printf("3. 查看用户信息\n");
    printf("4. 查看学习统计\n");
    printf("5. 生成学习报告\n");
    printf("6. 选择练习难度\n");
    printf("7. 🏆 查看排行榜\n");
    printf("8. ⚠️  注销用户\n");
    printf("9. 退出登录\n");
    printf("10. 退出程序\n");

    int choice;
    while (1) {
        setColor(COLOR_PROMPT);
        printf("请选择 (1 - 10): ");
        setColor(COLOR_DEFAULT);
        if (scanf_s("%d", &choice) == 1 && choice >= 1 && choice <= 10) {
            while (getchar() != '\n');
            return choice;
        } else {
            setColor(COLOR_WRONG);
            printf("输入无效，请重试！\n");
            setColor(COLOR_DEFAULT);
            while (getchar() != '\n');
            Sleep(1000);
        }
    }
}

Difficulty selectPracticeDifficulty() {
    int choice_char;
    while (1) {
        clearScreen();
        displayWelcome();

        setColor(COLOR_PROMPT);
        printf("选择练习难度\n\n");
        setColor(COLOR_DEFAULT);
        printf("----------------------------------------------------------------\n");
        printf("                        选择练习难度                          \n");
        printf("----------------------------------------------------------------\n");
        printf("                                                                \n");
        printf("  [1] 入门级 - 10以内加减法                                   \n");
        printf("  [2] 进阶级 - 20以内混合运算，含括号                         \n");
        printf("  [3] 地狱级 - 50以内复杂混合运算，含括号                     \n");
        printf("                                                                \n");
        printf("----------------------------------------------------------------\n");

        setColor(COLOR_PROMPT);
        printf("\n请选择练习难度(1 - 3): ");
        setColor(COLOR_DEFAULT);

        choice_char = _getch();
        printf("%c\n", choice_char);

        if (choice_char >= '1' && choice_char <= '3') {
            Difficulty selected = (Difficulty)(choice_char - '1');
            setColor(COLOR_CORRECT);
            printf("\n已选择: ");
            switch (selected) {
            case ENTRY: printf("入门级"); break;
            case ADVANCED: printf("进阶级"); break;
            case HELL: printf("地狱级"); break;
            }
            printf(" 难度\n");
            setColor(COLOR_DEFAULT);
            Sleep(150);
            return selected;
        }
        else {
            setColor(COLOR_WRONG);
            printf("无效输入，请重新选择!\n");
            setColor(COLOR_DEFAULT);
            Sleep(1000);
        }
    }
}

// 显示排行榜
void displayRankingBoard() {
    int choice;

    while (1) {
        clearScreen();
        displayWelcome();

        setColor(COLOR_PROMPT);
        printf("🏆 总分排行榜 🏆\n\n");
        setColor(COLOR_DEFAULT);

        printf("================================================================\n");
        printf("                        总分排行榜                            \n");
        printf("================================================================\n");
        printf("                                                                \n");
        printf("  计分规则：                                                   \n");
        printf("  • 入门级：每题 1 分                                         \n");
        printf("  • 进阶级：每题 3 分                                         \n");
        printf("  • 地狱级：每题 5 分                                         \n");
        printf("                                                                \n");
        printf("  [1] 查看排行榜                                               \n");
        printf("  [2] 返回主菜单                                               \n");
        printf("                                                                \n");
        printf("================================================================\n");

        setColor(COLOR_PROMPT);
        printf("\n请选择 (1-2): ");
        setColor(COLOR_DEFAULT);

        if (scanf_s("%d", &choice) == 1 && choice >= 1 && choice <= 2) {
            while (getchar() != '\n'); // 清除输入缓冲区

            if (choice == 2) {
                return; // 返回主菜单
            }

            RankingUser rankings[50]; // 最多显示50个用户
            int count = getTotalScoreRanking(rankings, 50);

            if (count == 0) {
                clearScreen();
                displayWelcome();
                setColor(COLOR_WRONG);
                printf("\n暂无用户数据！\n");
                setColor(COLOR_DEFAULT);
                printf("\n按任意键继续...");
                _getch();
                continue;
            }

            // 按总分排序
            sortRankingByTotalScore(rankings, count);

            // 显示排行榜
            clearScreen();
            displayWelcome();

            setColor(COLOR_PROMPT);
            printf("🏆 总分排行榜 🏆\n\n");
            setColor(COLOR_DEFAULT);

            printf("================================================================\n");
            printf("排名  用户名             总分    题目数  正确数  正确率      \n");
            printf("================================================================\n");

            // 显示前10名或所有用户（如果少于10个）
            int display_count = count > 10 ? 10 : count;
            for (int i = 0; i < display_count; i++) {
                // 根据排名设置颜色
                if (i == 0) {
                    setColor(COLOR_LEVEL_UP); // 第一名用特殊颜色
                    printf("🥇");
                } else if (i == 1) {
                    setColor(COLOR_CORRECT); // 第二名用绿色
                    printf("🥈");
                } else if (i == 2) {
                    setColor(COLOR_PROMPT); // 第三名用黄色
                    printf("🥉");
                } else {
                    setColor(COLOR_DEFAULT);
                    printf("  ");
                }

                // 计算用户名的实际显示宽度
                int display_width = 0;
                for (int k = 0; rankings[i].username[k] != '\0'; k++) {
                    if ((unsigned char)rankings[i].username[k] >= 0x80) {
                        display_width += 2; // 中文字符占2个显示位置
                    } else {
                        display_width += 1; // 英文字符占1个显示位置
                    }
                }

                // 输出排名和用户名
                printf("%-2d  %s", i + 1, rankings[i].username);

                // 补充空格以对齐到固定位置（目标宽度18）
                int spaces_needed = 18 - display_width;
                for (int s = 0; s < spaces_needed; s++) {
                    printf(" ");
                }

                // 输出其他数据，使用制表符对齐
                printf("%-8d%-8d%-8d%8.2f%%\n",
                    rankings[i].total_score,
                    rankings[i].questions_count,
                    rankings[i].correct_count,
                    rankings[i].accuracy);
            }

            setColor(COLOR_DEFAULT);
            printf("================================================================\n");

            if (count > 10) {
                setColor(COLOR_PROMPT);
                printf("\n注：仅显示前10名用户，共有 %d 名用户参与该难度练习\n", count);
                setColor(COLOR_DEFAULT);
            }

            printf("\n按任意键返回排行榜菜单...");
            _getch();
        } else {
            setColor(COLOR_WRONG);
            printf("输入无效，请重试！\n");
            setColor(COLOR_DEFAULT);
            while (getchar() != '\n'); // 清除输入缓冲区
            Sleep(1000);
        }
    }
}

// 注销当前用户
int deleteCurrentUser(const char* username) {
    char confirmation[10];

    clearScreen();
    displayWelcome();

    setColor(COLOR_WRONG);
    printf("⚠️  用户注销确认 ⚠️\n\n");
    setColor(COLOR_DEFAULT);

    printf("================================================================\n");
    printf("                        注意事项                              \n");
    printf("================================================================\n");
    printf("                                                                \n");
    printf("  您即将注销用户：%s\n", username);
    printf("                                                                \n");
    printf("  注销后将删除以下所有数据：                                   \n");
    printf("  • 用户账户信息                                               \n");
    printf("  • 所有答题记录                                               \n");
    printf("  • 学习统计数据                                               \n");
    printf("  • 排行榜记录                                                 \n");
    printf("                                                                \n");
    setColor(COLOR_WRONG);
    printf("  ⚠️  此操作无法撤销！⚠️                                      \n");
    setColor(COLOR_DEFAULT);
    printf("                                                                \n");
    printf("================================================================\n");

    setColor(COLOR_PROMPT);
    printf("\n请输入 'DELETE' 确认注销，或输入其他内容取消: ");
    setColor(COLOR_DEFAULT);

    if (fgets(confirmation, sizeof(confirmation), stdin) == NULL) {
        return 0;
    }
    confirmation[strcspn(confirmation, "\n")] = '\0';

    if (strcmp(confirmation, "DELETE") == 0) {
        if (deleteUser(username)) {
            setColor(COLOR_CORRECT);
            printf("\n✓ 用户 '%s' 已成功注销！\n", username);
            printf("所有相关数据已删除。\n");
            setColor(COLOR_DEFAULT);
            Sleep(2000);
            return 1; // 注销成功
        } else {
            setColor(COLOR_WRONG);
            printf("\n✗ 注销失败！用户可能不存在或发生错误。\n");
            setColor(COLOR_DEFAULT);
            Sleep(2000);
            return 0;
        }
    } else {
        setColor(COLOR_PROMPT);
        printf("\n注销操作已取消。\n");
        setColor(COLOR_DEFAULT);
        Sleep(1500);
        return 0; // 取消注销
    }
}