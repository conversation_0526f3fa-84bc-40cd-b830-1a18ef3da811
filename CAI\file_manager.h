#ifndef FILE_MANAGER_H
#define FILE_MANAGER_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "global_defs.h"

// --- 文件名常量 ---
#define USER_DATA_FILE "users.dat"//存储所有注册用户的基本信息和学习进度(二进制文件)
#define RECORDS_FILE "records.dat"//存储所有用户的详细答题记录(二进制文件)
#define CONFIG_FILE "config.txt"//存储程序的配置参数
#define REPORT_FILE "learning_report.txt"//生成和保存用户的学习报告

// --- 文件操作函数原型 ---

// 用户数据文件操作
int saveUserProfile(const UserProfile* user);                    // 保存用户档案
int loadUserByName(const char* username, UserProfile* user);     // 按用户名加载用户档案
int userNameExists(const char* username);                        // 检查用户名是否存在
int createNewUser(const char* name, const char* password, UserProfile* user); // 创建新用户
int deleteUser(const char* username);                            // 删除用户及其所有数据

// 题目记录文件操作
int saveQuestionRecord(const QuestionRecord* record);            // 保存题目记录
int loadUserRecords(const char* username, QuestionRecord records[], int max_records); // 加载用户记录
int getRecordCount(const char* username);                        // 获取用户记录数量

// 学习统计功能
void calculateLearningStats(const char* username, LearningStats* stats); // 计算学习统计
void updateUserStats(UserProfile* user, const QuestionRecord* record);  // 更新用户统计

// 配置文件操作
int saveConfig(const char* key, const char* value);              // 保存配置项
int loadConfig(const char* key, char* value, int max_len);       // 加载配置项

// 报告生成
int generateLearningReport(const char* username);                // 生成学习报告

// 排行榜功能
int getAllUsers(UserProfile users[], int max_users);             // 获取所有用户信息
int getTotalScoreRanking(RankingUser rankings[], int max_users); // 获取总分排行榜
void sortRankingByTotalScore(RankingUser rankings[], int count); // 按总分排序

// 文件工具函数
int fileExists(const char* filename);                            // 检查文件是否存在
int createDataDirectory();                                       // 创建数据目录
void initializeDataFiles();                                      // 初始化数据文件

#endif // FILE_MANAGER_H
