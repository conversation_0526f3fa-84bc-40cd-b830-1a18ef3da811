@echo off
echo 正在测试编译...

REM 尝试使用 Visual Studio 编译器
where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo 找到 Visual Studio 编译器，开始编译...
    cd CAI
    cl /EHsc /D_CRT_SECURE_NO_WARNINGS test_compile.cpp /Fe:test.exe
    if %ERRORLEVEL% EQU 0 (
        echo 编译测试成功！
        test.exe
    ) else (
        echo 编译测试失败！
    )
    cd ..
) else (
    echo 未找到 Visual Studio 编译器
    echo 请在 Visual Studio Developer Command Prompt 中运行此脚本
)

pause
