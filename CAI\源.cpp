#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <conio.h> // 用于 _getch() 函数
#include <windows.h> // 用于 SetConsoleTextAttribute 和 GetStdHandle 函数
#include <locale.h> // 用于设置本地化

// --- 包含自定义头文件 ---
#include "global_defs.h"
#include "console_utils.h"
#include "expression_evaluator.h"
#include "question_generator.h"
#include "file_manager.h"

// --- 主程序 ---
int main() {

    SetConsoleCP(65001);        // 设置控制台输入编码为UTF-8
    SetConsoleOutputCP(65001);  // 设置控制台输出编码为UTF-8
    setlocale(LC_ALL, "zh_CN.UTF-8");

    UserProfile current_user;      // 当前用户信息
    Difficulty practice_difficulty; // 用户选择的练习难度
    char question[MAX_EXPR_LEN];   // 存储生成的题目字符串
    int answer;                    // 用户输入的答案
    int correct_answer;            // 题目的正确答案
    char input_buffer[MAX_EXPR_LEN]; // 用于读取用户输入的缓冲区
    time_t question_start_time;    // 题目开始时间

    srand((unsigned int)time(NULL)); // 使用当前时间作为随机数种子

    // 初始化数据文件
    initializeDataFiles();

    // 用户登录/注册循环
    while (1) {
        int login_choice = showLoginMenu();

        if (login_choice == 3) { // 退出程序
            break;
        }
        else if (login_choice == 1) { // 用户登录
            if (userLogin(&current_user)) {
                break; // 登录成功，跳出循环
            }
        }
        else if (login_choice == 2) { // 用户注册
            if (userRegister(&current_user)) {
                break; // 注册成功，跳出循环
            }
        }
    }

    // 如果没有成功登录，退出程序
    if (strlen(current_user.name) == 0) {
        clearScreen();
        displayWelcome();
        setColor(COLOR_PROMPT);
        printf("\n感谢使用计算机辅助教学软件!\n");
        setColor(COLOR_DEFAULT);
        return 0;
    }

    // 初始化练习难度为用户当前难度
    practice_difficulty = current_user.current_difficulty;

    // 主程序循环（菜单驱动）
    while (1) {
        int choice = showMainMenuWithUser(&current_user); // 显示带用户信息的主菜单

        if (choice == 10) { // 选择 '10' 退出程序
            break;
        }
        else if (choice == 9) { // 选择 '9' 退出登录
            setColor(COLOR_PROMPT);
            printf("\n已退出登录，感谢使用!\n");
            setColor(COLOR_DEFAULT);
            Sleep(1500);
            return main(); // 重新启动程序
        }
        else if (choice == 8) { // 选择 '8' 注销用户
            if (deleteCurrentUser(current_user.name)) {
                // 注销成功，返回登录界面
                setColor(COLOR_PROMPT);
                printf("\n返回登录界面...\n");
                setColor(COLOR_DEFAULT);
                Sleep(1000);
                return main(); // 重新启动程序
            }
            // 注销失败或取消，继续显示主菜单
            continue;
        }
        else if (choice == 7) { // 选择 '7' 查看排行榜
            displayRankingBoard();
            continue;
        }
        else if (choice == 6) { // 选择 '6' 选择练习难度
            practice_difficulty = selectPracticeDifficulty();
            continue; // 返回主菜单
        }
        else if (choice == 2) { // 选择 '2' 查看帮助
            clearScreen();
            displayWelcome();
            displayHelp();
            setColor(COLOR_PROMPT);
            printf("\n按任意键返回主菜单...");
            setColor(COLOR_DEFAULT);
            _getch(); // 等待用户按键
            continue; // 返回主菜单循环
        }
        else if (choice == 3) { // 选择 '3' 查看用户信息
            clearScreen();
            displayWelcome();
            displayUserInfo(&current_user);
            setColor(COLOR_PROMPT);
            printf("\n按任意键返回主菜单...");
            setColor(COLOR_DEFAULT);
            _getch();
            continue;
        }
        else if (choice == 4) { // 选择 '4' 查看学习统计
            clearScreen();
            displayWelcome();
            displayLearningStats(current_user.name);
            setColor(COLOR_PROMPT);
            printf("\n按任意键返回主菜单...");
            setColor(COLOR_DEFAULT);
            _getch();
            continue;
        }
        else if (choice == 5) { // 选择 '5' 生成学习报告
            if (generateLearningReport(current_user.name)) {
                setColor(COLOR_CORRECT);
                printf("\n学习报告已追加到 %s 文件!\n", REPORT_FILE);
            } else {
                setColor(COLOR_WRONG);
                printf("\n生成学习报告失败!\n");
            }
            setColor(COLOR_DEFAULT);
            setColor(COLOR_PROMPT);
            printf("按任意键返回主菜单...");
            setColor(COLOR_DEFAULT);
            _getch();
            continue;
        }
        else if (choice != 1) { // 无效的菜单选择
            continue; // 重新显示主菜单
        }

        // 练习模式循环
        while (1) {
            clearScreen();
            displayWelcome();
            displayStatus(practice_difficulty, current_user.current_session_score); // 显示练习难度和得分

            generateQuestion(practice_difficulty, question); // 生成新题目

            int calc_error = 0; // 计算错误标志
            correct_answer = calculate(question, &calc_error); // 计算题目正确答案

            if (calc_error) { // 如果在生成或计算题目时出错
                setColor(COLOR_WRONG);
                printf("\n生成题目或计算时出错，重新生成题目...\n");
                setColor(COLOR_DEFAULT);
                Sleep(1500); // 暂停显示错误信息
                continue; // 跳过当前题目，生成下一题
            }

            int attempts = 0;          // 当前题目的尝试次数
            int answered_correctly = 0; // 答题状态：0-未答对，1-答对，-1-用户选择退出
            question_start_time = time(NULL); // 记录开始答题时间

            while (attempts < MAX_ATTEMPTS) { // 在最大尝试次数内循环
                displayQuestion(question); // 显示题目

                setColor(COLOR_PROMPT);
                printf("请输入答案 (输入 'q' 返回主菜单): ");
                setColor(COLOR_DEFAULT);

                // 使用 fgets 安全读取用户输入整行字符串
                if (fgets(input_buffer, sizeof(input_buffer), stdin) == NULL) {
                    continue; // 读取输入错误，继续循环
                }
                // 移除 fgets 读取到的换行符（如果存在）
                input_buffer[strcspn(input_buffer, "\n")] = '\0';

                // 检查用户是否输入 'q' 以退出练习模式
                if (_stricmp(input_buffer, "q") == 0) { // 不区分大小写比较
                    answered_correctly = -1; // 设置退出标志
                    break; // 退出尝试循环
                }

                // 尝试将字符串转换为整数
                if (sscanf_s(input_buffer, "%d", &answer) != 1) { // 如果转换失败（不是有效数字）
                    setColor(COLOR_WRONG);
                    printf("无效输入！请输入一个整数。\n");
                    setColor(COLOR_DEFAULT);
                    attempts++; // 计为一次尝试
                    Sleep(1000); // 暂停显示错误信息
                    printf("\n(剩余尝试次数: %d)\n", MAX_ATTEMPTS - attempts);
                    continue; // 继续下一次尝试
                }

                if (answer == correct_answer) { // 答案正确
                    setColor(COLOR_CORRECT);
                    printf("\n✓ 回答正确!\n");

                    // 根据难度级别实时加分
                    int points = 0;
                    switch (practice_difficulty) {
                    case ENTRY: points = 1; break;      // 入门级1分
                    case ADVANCED: points = 3; break;   // 进阶级3分
                    case HELL: points = 5; break;       // 地狱级5分
                    }
                    current_user.total_score += points;
                    current_user.current_session_score++; // 会话得分增加

                    setColor(COLOR_LEVEL_UP);
                    printf("获得 %d 分！总分: %d\n", points, current_user.total_score);
                    setColor(COLOR_DEFAULT);

                    answered_correctly = 1; // 设置答对标志
                    break; // 退出尝试循环
                }
                else { // 答案错误
                    setColor(COLOR_WRONG);
                    printf("\n✗ 回答错误!");
                    attempts++; // 尝试次数增加
                    if (attempts < MAX_ATTEMPTS) {
                        printf(" 请再试一次 (剩余尝试次数: %d)\n", MAX_ATTEMPTS - attempts);
                    }
                    else {
                        printf(" 正确答案是: %d\n", correct_answer); // 尝试次数用尽，显示正确答案
                    }
                    setColor(COLOR_DEFAULT);
                    Sleep(1500); // 暂停显示反馈信息。
                }
            } // 尝试循环结束

            // 创建题目记录
            QuestionRecord record;
            strncpy_s(record.username, MAX_NAME_LEN, current_user.name, _TRUNCATE);
            strncpy_s(record.question, MAX_EXPR_LEN, question, _TRUNCATE);
            record.correct_answer = correct_answer;
            record.user_answer = (answered_correctly == 1) ? answer : (attempts == MAX_ATTEMPTS ? answer : -1);
            record.attempts_used = (answered_correctly == 1) ? attempts + 1 : MAX_ATTEMPTS;
            record.is_correct = (answered_correctly == 1) ? 1 : 0;
            record.difficulty = practice_difficulty;
            record.timestamp = time(NULL);
            record.time_spent = difftime(time(NULL), question_start_time);

            // 保存题目记录
            saveQuestionRecord(&record);

            // 更新用户统计
            updateUserStats(&current_user, &record);

            if (answered_correctly == -1) { // 如果用户选择退出练习模式
                break; // 退出练习模式循环，返回主菜单
            }

            // 每道题结束（无论答对或尝试次数用尽）后，检查升级/降级
            if (current_user.current_session_score >= MAX_SCORE) { // 达到升级分数
                if (current_user.current_difficulty < HELL) {
                    current_user.current_difficulty = (Difficulty)(current_user.current_difficulty + 1); // 升级
                    setColor(COLOR_LEVEL_UP);
                    printf("\n★★★ 恭喜! 您已升级到下一级别! ★★★\n");
                    setColor(COLOR_DEFAULT);
                    current_user.current_session_score = 0; // 升级后分数清零
                    current_user.total_score += MAX_SCORE; // 增加总分
                    Sleep(2000);
                }
                else {
                    setColor(COLOR_PROMPT);
                    printf("\n您已达到最高难度级别，继续挑战吧！\n");
                    setColor(COLOR_DEFAULT);
                    current_user.current_session_score = MAX_SCORE; // 保持分数在最高
                    Sleep(2000);
                }
            }
            else if (current_user.current_session_score < MIN_SCORE) {
                if (current_user.current_difficulty > ENTRY) {
                    current_user.current_difficulty = (Difficulty)(current_user.current_difficulty - 1); // 降级
                    setColor(COLOR_WRONG);
                    printf("\n⚠️ 注意: 您已降级到下一级别，请继续努力!\n");
                    setColor(COLOR_DEFAULT);
                    current_user.current_session_score = 0; // 降级后分数清零
                    Sleep(2000);
                }
                else {
                    setColor(COLOR_PROMPT);
                    printf("\n您已在最低难度级别，请继续努力提升！\n");
                    setColor(COLOR_DEFAULT);
                    current_user.current_session_score = 0; // 保持分数在最低
                    Sleep(2000);
                }
            }

            // 保存更新的用户数据
            saveUserProfile(&current_user);

            // 暂停，等待用户按任意键继续下一题
            if (answered_correctly == 1 || attempts == MAX_ATTEMPTS) {
                setColor(COLOR_PROMPT);
                printf("\n按任意键继续下一题...\n");
                setColor(COLOR_DEFAULT);
                _getch();
            }

        }
    }

    // 程序退出信息
    clearScreen();
    displayWelcome();
    setColor(COLOR_PROMPT);
    printf("\n感谢使用计算机辅助教学软件!\n");
    setColor(COLOR_DEFAULT);

    return 0;
}